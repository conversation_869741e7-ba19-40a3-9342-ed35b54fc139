# Journii AI Travel Agent PoC

A Flask-based AI travel agent that uses <PERSON> (via OpenRouter) to help users search and book flights.

## Features

- Structured travel request processing
- AI-powered flight search using Claude
- Mock flight booking system
- RESTful API endpoints

## Setup

1. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

2. Copy environment variables:
   ```bash
   cp .env.example .env
   ```

3. Add your OpenRouter API key to `.env`:
   ```
   OPENROUTER_API_KEY=your_actual_api_key_here
   ```

4. Run the application:
   ```bash
   python app.py
   ```

## API Endpoints

- `POST /api/travel-request` - Submit a travel request
- `POST /api/book-flight` - Confirm flight booking

## Example Request

```json
{
  "origin": "New York",
  "destination": "London",
  "departure_date": "2024-03-15",
  "return_date": "2024-03-22",
  "passengers": 2,
  "class": "economy"
}
```
